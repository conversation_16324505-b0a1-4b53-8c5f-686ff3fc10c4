import React from 'react';
import Image from 'next/image';
import Link from 'next/link';

// Usunieto niepotrzebne dane i komponenty dla ultra luksusowego minimalizmu

const HeroSection = () => {
  return (
    <section className="h-screen flex items-center justify-center bg-silk relative overflow-hidden">
      {/* Background image bez parallax */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20"
        style={{ backgroundImage: 'url(/images/background/bali-hero.webp)' }}
      />

      <div className="relative z-10 text-center max-w-luxury mx-auto px-[16.67%]">
        {/* Scarcity Signal */}
        <div
          className="text-micro uppercase tracking-[0.05em] text-ink/60 mb-16 opacity-0 animate-fade-in"
          style={{ animationDelay: '0.2s', animationFillMode: 'forwards' }}
        >
          Only 8 spaces available • Next retreat: September 2025
        </div>

        {/* Hero Title - nowy layout */}
        <h1
          className="text-hero font-extralight text-ink mb-8 opacity-0 animate-fade-in"
          style={{
            animationDelay: '0.8s',
            animationFillMode: 'forwards',
            lineHeight: '1.1',
            letterSpacing: '-0.03em'
          }}
        >
          BALI YOGA<br />JOURNEY
        </h1>

        {/* Subtitle */}
        <p
          className="text-body text-ink/70 mb-16 opacity-0 animate-fade-in max-w-md mx-auto"
          style={{ animationDelay: '1.2s', animationFillMode: 'forwards' }}
        >
          Transform your practice in paradise
        </p>

        {/* CTA Button - jedyny złoty element */}
        <Link
          href="#luxury-experience"
          className="inline-block bg-temple-gold text-silk px-12 py-4 text-sm font-light tracking-[0.1em] uppercase transition-all duration-400 hover:opacity-70 opacity-0 animate-fade-in"
          style={{ animationDelay: '1.6s', animationFillMode: 'forwards' }}
          aria-label="Begin your luxury yoga journey"
        >
          Begin Journey
        </Link>
      </div>
    </section>
  );
};

// Usunieto komponent Card - nie potrzebny w ultra minimalistycznym designie

// Usunieto wszystkie niepotrzebne dane dla ultra minimalizmu

export default function HomePage() {

  return (
    <div className="relative bg-silk">
      <HeroSection />

      {/* LUXURY EXPERIENCE - nowa sekcja */}
      <section id="luxury-experience" className="py-48 md:py-64 relative">
        <div className="max-w-luxury mx-auto px-[16.67%]">
          {/* Tekst po lewej (30%) + Obraz po prawej (50%) + 20% pusta przestrzeń */}
          <div className="grid grid-cols-12 gap-16 items-center">
            <div className="col-span-4">
              <div className="text-micro uppercase tracking-[0.05em] text-ink/60 mb-8">Our Destinations</div>
              <h2 className="text-h2 font-extralight text-ink mb-12">
                Curated<br />Experiences
              </h2>
              <p className="text-body text-ink/70 mb-8">
                Immerse yourself in the sacred landscapes of Bali and Sri Lanka, where ancient wisdom meets modern luxury.
              </p>
              <p className="text-body text-ink/70">
                Each retreat is meticulously designed for transformation through mindful practice and cultural immersion.
              </p>
            </div>
            <div className="col-span-6 col-start-6">
              <div className="relative h-[600px] bg-sand">
                <Image
                  src="/images/blog/temple-bali.webp"
                  alt="Luxury yoga retreat in Bali"
                  fill
                  className="object-cover"
                  sizes="50vw"
                />
              </div>
            </div>
          </div>
        </div>
      </section>
        </div>
      </section>

      {/* PHILOSOPHY - druga sekcja */}
      <section className="py-48 md:py-64 bg-sand relative">
        <div className="max-w-luxury mx-auto px-[16.67%]">
          {/* Odwrócony layout - Obraz po lewej + Tekst po prawej */}
          <div className="grid grid-cols-12 gap-16 items-center">
            <div className="col-span-6">
              <div className="relative h-[600px] bg-silk">
                <Image
                  src="/images/blog/handstand-828.webp"
                  alt="Yoga practice philosophy"
                  fill
                  className="object-cover"
                  sizes="50vw"
                />
              </div>
            </div>
            <div className="col-span-4 col-start-8">
              <div className="text-micro uppercase tracking-[0.05em] text-ink/60 mb-8">Our Philosophy</div>
              <h2 className="text-h2 font-extralight text-ink mb-12">
                Mindful<br />Transformation
              </h2>
              <p className="text-body text-ink/70 mb-8">
                Beyond physical practice lies a deeper journey of self-discovery and inner awakening.
              </p>
              <p className="text-body text-ink/70">
                Our approach integrates traditional yoga wisdom with contemporary wellness practices.
              </p>
            </div>
          </div>
        </div>
      </section>
      {/* SOCIAL PROOF - minimalistyczny */}
      <section className="py-48 md:py-64 relative">
        <div className="max-w-luxury mx-auto px-[16.67%] text-center">
          <div className="text-micro uppercase tracking-[0.05em] text-ink/60 mb-16">Trusted by</div>

          {/* Minimalistyczne testimonials */}
          <div className="grid grid-cols-3 gap-16 mb-24">
            {[
              { name: 'Marta K.', title: 'Architect', location: 'Warsaw' },
              { name: 'Tomasz W.', title: 'Designer', location: 'Krakow' },
              { name: 'Anna M.', title: 'Entrepreneur', location: 'Wroclaw' }
            ].map((person, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-sand rounded-full mx-auto mb-4"></div>
                <div className="text-body font-light text-ink mb-1">{person.name}</div>
                <div className="text-micro uppercase tracking-[0.05em] text-ink/60">{person.title}</div>
              </div>
            ))}
          </div>
        </div>
      </section>
      {/* FINAL CTA - ultra luksusowe */}
      <section className="py-48 md:py-64 bg-sand relative">
        <div className="max-w-luxury mx-auto px-[16.67%] text-center">
          <h2 className="text-h2 font-extralight text-ink mb-16">
            Ready to<br />Transform?
          </h2>

          <p className="text-body text-ink/70 mb-24 max-w-md mx-auto">
            Join our next exclusive retreat and discover the profound connection between mind, body, and spirit.
          </p>

          <Link
            href="/kontakt"
            className="inline-block bg-temple-gold text-silk px-16 py-5 text-sm font-light tracking-[0.1em] uppercase transition-all duration-400 hover:opacity-70"
            aria-label="Reserve your space"
          >
            Reserve Your Space
          </Link>
        </div>
      </section>


    </div>
  );
}
